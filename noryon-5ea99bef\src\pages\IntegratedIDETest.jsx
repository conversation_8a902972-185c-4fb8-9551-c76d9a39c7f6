import React from 'react';
import IntegratedCodeEditorTerminal from '../components/IntegratedCodeEditorTerminal';

const IntegratedIDETest = () => {
  const sampleFiles = {
    'src/App.jsx': `import React, { useState } from 'react'
import './App.css'

function App() {
  const [count, setCount] = useState(0)

  return (
    <div className="App">
      <header className="App-header">
        <h1>Welcome to My App</h1>
        <div className="card">
          <button onClick={() => setCount((count) => count + 1)}>
            count is {count}
          </button>
          <p>Edit and save to test the integrated IDE</p>
        </div>
      </header>
    </div>
  )
}

export default App`,
    'src/App.css': `#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  border-radius: 8px;
}

.card {
  padding: 2em;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  color: white;
  cursor: pointer;
  transition: border-color 0.25s;
}

button:hover {
  border-color: #646cff;
}`
  };

  return (
    <div className="min-h-screen bg-gray-900">
      <div className="p-4">
        <h1 className="text-2xl font-bold text-white mb-4">Integrated Code Editor & Terminal Test</h1>
        <div className="w-full h-[80vh]">
          <IntegratedCodeEditorTerminal 
            initialFiles={sampleFiles}
            onFilesChange={(files) => console.log('Files changed:', files)}
            onSave={(files, fileName) => console.log('Saved:', fileName)}
          />
        </div>
      </div>
    </div>
  );
};

export default IntegratedIDETest;