import Layout from "./Layout.jsx";

import Generator from "./Generator";

import MyApps from "./MyApps";

import AppPreview from "./AppPreview";

import Profile from "./Profile";

import Pricing from "./Pricing";

import Help from "./Help";

import Community from "./Community";

import Dashboard from "./Dashboard";

import AppDashboard from "./AppDashboard";

import Teams from "./Teams";

import TeamDetail from "./TeamDetail";

import TemplateDetail from "./TemplateDetail";

import CreatorDashboard from "./CreatorDashboard";

import CreatorSignup from "./CreatorSignup";

import AgentGallery from "./AgentGallery";

import CreateWithAgent from "./CreateWithAgent";

import AppAnalytics from "./AppAnalytics";

import AppDomains from "./AppDomains";

import AppLogs from "./AppLogs";

import CreateDesignAgent from "./CreateDesignAgent";

import TemplateBuilder from "./TemplateBuilder";
import SignIn from "./SignIn";
import CodeIDE from "./CodeIDE";
import IntegratedIDETest from "./IntegratedIDETest";

import { BrowserRouter as Router, Route, Routes, useLocation } from 'react-router-dom';

const PAGES = {
    
    Generator: Generator,
    
    MyApps: MyApps,
    
    AppPreview: AppPreview,
    
    Profile: Profile,
    
    Pricing: Pricing,
    
    Help: Help,
    
    Community: Community,
    
    Dashboard: Dashboard,
    
    AppDashboard: AppDashboard,
    
    Teams: Teams,
    
    TeamDetail: TeamDetail,
    
    TemplateDetail: TemplateDetail,
    
    CreatorDashboard: CreatorDashboard,
    
    CreatorSignup: CreatorSignup,
    
    AgentGallery: AgentGallery,
    
    CreateWithAgent: CreateWithAgent,
    
    AppAnalytics: AppAnalytics,
    
    AppDomains: AppDomains,
    
    AppLogs: AppLogs,
    
    CreateDesignAgent: CreateDesignAgent,
    
    TemplateBuilder: TemplateBuilder,
    SignIn: SignIn,
    CodeIDE: CodeIDE,
    IntegratedIDETest: IntegratedIDETest,
    
}

function _getCurrentPage(url) {
    if (url.endsWith('/')) {
        url = url.slice(0, -1);
    }
    let urlLastPart = url.split('/').pop();
    if (urlLastPart.includes('?')) {
        urlLastPart = urlLastPart.split('?')[0];
    }

    const pageName = Object.keys(PAGES).find(page => page.toLowerCase() === urlLastPart.toLowerCase());
    return pageName || Object.keys(PAGES)[0];
}

// Create a wrapper component that uses useLocation inside the Router context
function PagesContent() {
    const location = useLocation();
    const currentPage = _getCurrentPage(location.pathname);
    
    return (
        <Layout currentPageName={currentPage}>
            <Routes>            
                
                    <Route path="/" element={<Generator />} />
                
                
                <Route path="/Generator" element={<Generator />} />
                
                <Route path="/MyApps" element={<MyApps />} />
                
                <Route path="/AppPreview" element={<AppPreview />} />
                
                <Route path="/Profile" element={<Profile />} />
                
                <Route path="/Pricing" element={<Pricing />} />
                
                <Route path="/Help" element={<Help />} />
                
                <Route path="/Community" element={<Community />} />
                
                <Route path="/Dashboard" element={<Dashboard />} />
                
                <Route path="/AppDashboard" element={<AppDashboard />} />
                
                <Route path="/Teams" element={<Teams />} />
                
                <Route path="/TeamDetail" element={<TeamDetail />} />
                
                <Route path="/TemplateDetail" element={<TemplateDetail />} />
                
                <Route path="/CreatorDashboard" element={<CreatorDashboard />} />
                
                <Route path="/CreatorSignup" element={<CreatorSignup />} />
                
                <Route path="/AgentGallery" element={<AgentGallery />} />
                
                <Route path="/CreateWithAgent" element={<CreateWithAgent />} />
                
                <Route path="/AppAnalytics" element={<AppAnalytics />} />
                
                <Route path="/AppDomains" element={<AppDomains />} />
                
                <Route path="/AppLogs" element={<AppLogs />} />
                
                <Route path="/CreateDesignAgent" element={<CreateDesignAgent />} />
                
                <Route path="/TemplateBuilder" element={<TemplateBuilder />} />
                <Route path="/SignIn" element={<SignIn />} />
                <Route path="/CodeIDE" element={<CodeIDE />} />
                <Route path="/IntegratedIDETest" element={<IntegratedIDETest />} />
                
            </Routes>
        </Layout>
    );
}

export default function Pages() {
    return (
        <Router>
            <PagesContent />
        </Router>
    );
}